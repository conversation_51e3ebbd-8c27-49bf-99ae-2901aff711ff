using System.IO.Abstractions.TestingHelpers;
using NuGone.Cli.Features.AnalyzeCommand.Commands;
using NuGone.Cli.Shared.Constants;
using NuGone.Cli.Shared.Models;
using NuGone.Cli.Shared.Utilities;
using Shouldly;
using Shouldly;
using Spectre.Console.Cli;
using Spectre.Console.Testing;
using Xunit;

namespace NuGone.Cli.Tests.Commands;

/// <summary>
/// Tests for AnalyzeCommand class.
/// Validates RFC-0001: CLI Architecture And Command Design - AnalyzeCommand implementation.
/// </summary>
public class AnalyzeCommandTests
{
    private readonly MockFileSystem _fileSystem;
    private readonly string _testProjectPath;
    private readonly string _testSolutionPath;

    public AnalyzeCommandTests()
    {
        _fileSystem = new MockFileSystem();
        _testProjectPath = Path.Combine("test", "project", "Test.csproj");
        _testSolutionPath = Path.Combine("test", "solution", "Test.sln");

        // Setup test files
        _fileSystem.AddFile(_testProjectPath, new MockFileData("<Project></Project>"));
        _fileSystem.AddFile(
            _testSolutionPath,
            new MockFileData("Microsoft Visual Studio Solution File")
        );
    }

    #region Settings Tests

    [Fact]
    public void Settings_ShouldHaveCorrectDefaultValues()
    {
        // Arrange & Act
        var settings = new AnalyzeCommand.Settings();

        // Assert
        settings.ProjectPath.ShouldBeNull();
        settings.DryRun.ShouldBeTrue(); // Analyze is always dry-run by nature
        settings.Format.ShouldBe("text");
        settings.OutputFile.ShouldBeNull();
        settings.ExcludePackages.ShouldBeNull();
        settings.Verbose.ShouldBeFalse();
    }

    [Fact]
    public void Settings_ShouldAcceptProjectPath()
    {
        // Arrange & Act
        var settings = new AnalyzeCommand.Settings { ProjectPath = "/path/to/project" };

        // Assert
        settings.ProjectPath.ShouldBe("/path/to/project");
    }

    [Fact]
    public void Settings_ShouldAcceptFormatOptions()
    {
        // Arrange & Act
        var jsonSettings = new AnalyzeCommand.Settings { Format = "json" };
        var textSettings = new AnalyzeCommand.Settings { Format = "text" };

        // Assert
        jsonSettings.Format.ShouldBe("json");
        textSettings.Format.ShouldBe("text");
    }

    [Fact]
    public void Settings_ShouldAcceptExcludePackages()
    {
        // Arrange & Act
        var settings = new AnalyzeCommand.Settings
        {
            ExcludePackages = new[] { "Package1", "Package2" },
        };

        // Assert
        settings.ExcludePackages.ShouldNotBeNull();
        settings.ExcludePackages.ShouldHaveCount(2);
        settings.ExcludePackages.ShouldContain("Package1");
        settings.ExcludePackages.ShouldContain("Package2");
    }

    [Fact]
    public void Settings_ShouldAcceptVerboseFlag()
    {
        // Arrange & Act
        var settings = new AnalyzeCommand.Settings { Verbose = true };

        // Assert
        settings.Verbose.ShouldBeTrue();
    }

    [Fact]
    public void Settings_ShouldAcceptOutputFile()
    {
        // Arrange & Act
        var settings = new AnalyzeCommand.Settings { OutputFile = "output.json" };

        // Assert
        settings.OutputFile.ShouldBe("output.json");
    }

    #endregion

    #region Command Execution Tests

    [Fact]
    public void AnalyzeCommand_ShouldInheritFromBaseCommand()
    {
        // Arrange & Act
        var command = new AnalyzeCommand();

        // Assert
        command.ShouldBeAssignableTo<BaseCommand<AnalyzeCommand.Settings>>();
    }

    [Fact]
    public void AnalyzeCommand_ShouldImplementIAsyncCommand()
    {
        // Arrange & Act
        var command = new AnalyzeCommand();

        // Assert
        command.ShouldBeAssignableTo<IAsyncCommand<AnalyzeCommand.Settings>>();
    }

    [Fact]
    public void AnalyzeCommand_ShouldValidateProjectPath()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { ProjectPath = "/non/existent/path" };

        // Act
        var result = command.TestValidateAndResolveProjectPath(settings.ProjectPath);

        // Assert
        result.IsFailure.ShouldBeTrue();
        result.Error.Code.ShouldBe("INVALID_ARGUMENT");
        result.Error.Message.ShouldContain("Project path does not exist");
    }

    [Fact]
    public void AnalyzeCommand_ShouldUseCurrentDirectoryWhenProjectPathIsNull()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { ProjectPath = null };

        // Act
        var result = command.TestValidateAndResolveProjectPath(settings.ProjectPath);

        // Assert
        result.IsSuccess.ShouldBeTrue();
        result.Value.ShouldBe(Path.GetFullPath(Directory.GetCurrentDirectory()));
    }

    [Theory]
    [InlineData("json")]
    [InlineData("JSON")]
    [InlineData("Json")]
    public void AnalyzeCommand_ShouldAcceptJsonFormat(string format)
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = format };

        // Act
        var isJsonFormat = command.TestIsJsonFormat(settings);

        // Assert
        isJsonFormat.ShouldBeTrue();
    }

    [Theory]
    [InlineData("text")]
    [InlineData("TEXT")]
    [InlineData("Text")]
    [InlineData("")]
    [InlineData(null)]
    public void AnalyzeCommand_ShouldTreatNonJsonAsTextFormat(string? format)
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = format ?? string.Empty };

        // Act
        var isJsonFormat = command.TestIsJsonFormat(settings);

        // Assert
        isJsonFormat.ShouldBeFalse();
    }

    [Fact]
    public void AnalyzeCommand_ShouldDetectVerboseMode()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var verboseSettings = new AnalyzeCommand.Settings { Verbose = true };
        var nonVerboseSettings = new AnalyzeCommand.Settings { Verbose = false };

        // Act
        var isVerbose = command.TestIsVerboseMode(verboseSettings);
        var isNotVerbose = command.TestIsVerboseMode(nonVerboseSettings);

        // Assert
        isVerbose.ShouldBeTrue();
        isNotVerbose.ShouldBeFalse();
    }

    #endregion

    #region Output Format Tests

    [Fact]
    public void AnalyzeCommand_ShouldShowSuccessMessageForTextFormat()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "text", Verbose = false };

        // Act
        var shouldShowMessage = command.TestShouldShowSuccessMessage(settings);

        // Assert
        shouldShowMessage.ShouldBeTrue();
    }

    [Fact]
    public void AnalyzeCommand_ShouldNotShowSuccessMessageForJsonFormatWithoutVerbose()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "json", Verbose = false };

        // Act
        var shouldShowMessage = command.TestShouldShowSuccessMessage(settings);

        // Assert
        shouldShowMessage.ShouldBeFalse();
    }

    [Fact]
    public void AnalyzeCommand_ShouldShowSuccessMessageForJsonFormatWithVerbose()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "json", Verbose = true };

        // Act
        var shouldShowMessage = command.TestShouldShowSuccessMessage(settings);

        // Assert
        shouldShowMessage.ShouldBeTrue();
    }

    [Fact]
    public void AnalyzeCommand_ShouldShowProgressMessageForTextFormat()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "text", Verbose = false };

        // Act
        var shouldShowProgress = command.TestShouldShowProgressMessage(settings);

        // Assert
        shouldShowProgress.ShouldBeTrue();
    }

    [Fact]
    public void AnalyzeCommand_ShouldNotShowProgressMessageForJsonFormatWithoutVerbose()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "json", Verbose = false };

        // Act
        var shouldShowProgress = command.TestShouldShowProgressMessage(settings);

        // Assert
        shouldShowProgress.ShouldBeFalse();
    }

    [Fact]
    public void AnalyzeCommand_ShouldShowProgressMessageForJsonFormatWithVerbose()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "json", Verbose = true };

        // Act
        var shouldShowProgress = command.TestShouldShowProgressMessage(settings);

        // Assert
        shouldShowProgress.ShouldBeTrue();
    }

    #endregion

    #region Error Handling Tests

    [Fact]
    public void AnalyzeCommand_ShouldReturnErrorForInvalidProjectPath()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings
        {
            ProjectPath = "/invalid/path/that/does/not/exist",
        };

        // Act
        var result = command.TestValidateAndResolveProjectPath(settings.ProjectPath);

        // Assert
        result.IsFailure.ShouldBeTrue();
        result.Error.Code.ShouldBe("INVALID_ARGUMENT");
        result.Error.ExitCode.ShouldBe(ExitCodes.InvalidArgument);
    }

    #endregion

    #region Helper Classes

    /// <summary>
    /// Testable version of AnalyzeCommand that exposes protected methods for testing.
    /// </summary>
    private class TestableAnalyzeCommand : AnalyzeCommand
    {
        public Result<string> TestValidateAndResolveProjectPath(string? projectPath)
        {
            return ValidateAndResolveProjectPath(projectPath);
        }

        public bool TestIsVerboseMode(Settings settings)
        {
            return IsVerboseMode(settings);
        }

        public bool TestIsJsonFormat(Settings settings)
        {
            return settings.Format?.ToLowerInvariant() == "json";
        }

        public bool TestShouldShowSuccessMessage(Settings settings)
        {
            return settings.Format?.ToLowerInvariant() != "json" || settings.Verbose;
        }

        public bool TestShouldShowProgressMessage(Settings settings)
        {
            return settings.Format?.ToLowerInvariant() != "json" || settings.Verbose;
        }

        // Override to prevent actual execution during tests
        protected override async Task<Result<int>> ExecuteCommandAsync(
            CommandContext context,
            Settings settings
        )
        {
            await Task.CompletedTask;
            return ExitCodes.Success;
        }
    }

    #endregion
}
